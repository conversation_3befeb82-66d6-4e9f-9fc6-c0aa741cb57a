<?php

namespace Database\Seeders;

use App\Models\Category;
use App\Models\Content;
use App\Models\ContentKeyword;
use App\Models\ContentReview;
use App\Models\Keyword;
use App\Models\Role;
use App\Models\User;
use App\Helpers\StorageHelper;
use Carbon\Carbon;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Storage;
use ProtoneMedia\LaravelFFMpeg\Support\FFMpeg;
use Ramsey\Uuid\Uuid;

class ContentSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // Get users for author and reviewer
        $user = User::where('username', 'user')->first();
        $reviewer = User::where('username', 'reviewer')->first();

        if (!$user || !$reviewer) {
            $this->command->error('User or Reviewer not found. Please run DatabaseSeeder first.');
            return;
        }

        // Get categories
        $generalCategory = Category::where('type', 'general')->inRandomOrder()->first();
        $subjectCategory = Category::where('type', 'subject')->inRandomOrder()->first();

        if (!$generalCategory || !$subjectCategory) {
            $this->command->error('Categories not found. Please run CategorySeeder first.');
            return;
        }

        // Create or get keywords
        $keywords = $this->createKeywords();

        // Source folder
        $sourceFolder = base_path('untuk_seeder');

        // Check if folder exists
        if (!File::exists($sourceFolder)) {
            $this->command->error('Source folder not found: ' . $sourceFolder);
            return;
        }

        // Create storage directories if they don't exist
        Storage::disk('public')->makeDirectory('uploads');
        Storage::disk('public')->makeDirectory('thumbnail');

        // Media types and their corresponding files
        $mediaFiles = [
            'Photo' => ['photo.jpg', 'photo2.jpg', 'photo3.jpg'],
            'Illustration' => ['illustration.jpg', 'illustration2.jpg', 'illustration3.jpg'],
            'Video' => ['video.mp4', 'video2.mp4', 'video3.mp4'],
            'Audio' => ['audio.mp3', 'audio2.mp3', 'audio3.mp3'],
            'Document' => [
                'dokumen.pdf', 'dokumen.docx', 'data.xlsx',
                'dokumen2.pdf', 'dokumen2.docx', 'laporan.xls',
                'dokumen3.pdf', 'dokumen3.doc', 'data_makro.xlsm'
            ]
        ];

        // Status types to create
        // Note: We're removing 'Approved' since it's essentially the same as 'Published'
        $statuses = ['Draft', 'Pending', 'Rejected', 'Published'];

        // Get all categories for more variety
        $generalCategories = Category::where('type', 'general')->get();
        $subjectCategories = Category::where('type', 'subject')->get();

        // We'll use the default 'user' account for all content
        // This ensures that when you log in as 'user', you'll see all the content
        if (!$user) {
            $this->command->error("Default 'user' account not found. Please run UserSeeder first.");
            return;
        }

        // Sample descriptions for more variety
        $descriptions = [
            'Photo' => [
                'A beautiful landscape photograph showcasing nature at its finest.',
                'Portrait photography capturing the essence of human emotion.',
                'Urban photography highlighting the architecture of modern cities.',
                'Wildlife photography documenting rare animal species in their natural habitat.',
                'Macro photography revealing the intricate details of small objects.'
            ],
            'Illustration' => [
                'Digital illustration created with modern design software.',
                'Hand-drawn illustration showcasing traditional artistic techniques.',
                'Vector illustration perfect for commercial and marketing purposes.',
                'Conceptual illustration representing abstract ideas and concepts.',
                'Character design illustration for animation and storytelling.'
            ],
            'Video' => [
                'Short documentary film exploring important social issues.',
                'Tutorial video demonstrating step-by-step instructions.',
                'Animation video created with cutting-edge technology.',
                'Interview video featuring insights from industry experts.',
                'Promotional video showcasing products and services.'
            ],
            'Audio' => [
                'Original music composition with orchestral arrangements.',
                'Podcast episode discussing current events and trends.',
                'Sound effects library for multimedia production.',
                'Audiobook narration with professional voice acting.',
                'Ambient soundscape recording from natural environments.'
            ],
            'Document' => [
                'Research paper presenting findings from academic studies.',
                'Business proposal outlining strategic initiatives.',
                'Technical documentation for software and hardware systems.',
                'Educational curriculum for academic institutions.',
                'Legal document template for professional use.'
            ]
        ];

        foreach ($mediaFiles as $mediaType => $files) {
            $files = is_array($files) ? $files : [$files];

            foreach ($files as $fileIndex => $file) {
                $sourceFile = $sourceFolder . '/' . $file;

                if (!File::exists($sourceFile)) {
                    $this->command->error("Source file not found: $sourceFile");
                    continue;
                }

                // Always use the default 'user' account as author
                $author = $user;

                // Select random categories
                $generalCategory = $generalCategories->random();
                $subjectCategory = $subjectCategories->random();

                // Get description for this media type
                $mediaDescriptions = $descriptions[$mediaType];
                $description = $mediaDescriptions[$fileIndex % count($mediaDescriptions)];

                // For each file, create test items with specific statuses and dates
                $testItems = [
                    ['status' => 'Published', 'days_ago' => 5, 'was_approved' => true],   // Approved & Published within 14 days
                    ['status' => 'Published', 'days_ago' => 20, 'was_approved' => true],  // Approved & Published older than 14 days (will be hidden)
                    ['status' => 'Rejected', 'days_ago' => 10],   // Rejected within 14 days
                    ['status' => 'Rejected', 'days_ago' => 25],   // Rejected older than 14 days (will be archived)
                    ['status' => 'Draft', 'days_ago' => 1],       // Other statuses for variety
                    ['status' => 'Pending', 'days_ago' => 2],     // Other statuses for variety
                    ['status' => 'Published', 'days_ago' => 3, 'was_approved' => true]    // More approved content
                ];

                // Shuffle and pick 3-4 items to create
                shuffle($testItems);
                $testItems = array_slice($testItems, 0, rand(3, 4));

                foreach ($testItems as $item) {
                    $this->createContent(
                        $author->id,
                        $reviewer->id,
                        $generalCategory->id,
                        $subjectCategory->id,
                        $sourceFile,
                        $file . '_' . $item['status'] . '_' . $item['days_ago'] . 'd',
                        $mediaType,
                        $item['status'],
                        $keywords,
                        $description . ' - ' . $item['status'] . ' ' . $item['days_ago'] . ' days ago',
                        $item['days_ago'],
                        $item['was_approved'] ?? false
                    );
                }
            }
        }

        $this->command->info('Content seeding completed successfully!');
    }

    /**
     * Create content with the given parameters
     */
    private function createContent($authorId, $reviewerId, $categoryId, $category2Id, $sourceFile, $filename, $mediaType, $status, $keywords, $description = null, $daysAgo = 1, $wasApproved = false)
    {
        // Ensure user directories exist
        StorageHelper::ensureUserDirectoriesExist($authorId);

        // Generate a unique filename to avoid conflicts
        $uniqueFilename = pathinfo($filename, PATHINFO_FILENAME) . '_' . uniqid() . '.' . pathinfo($filename, PATHINFO_EXTENSION);

        // Get MIME type folder based on media type
        $mimeType = $this->getMimeTypeFromMediaType($mediaType);

        // Get user-specific storage path
        $filePath = StorageHelper::getUserStoragePath($authorId, $mimeType, $uniqueFilename);

        // Create directory if it doesn't exist
        $directory = dirname(storage_path('app/public/' . $filePath));
        if (!File::exists($directory)) {
            File::makeDirectory($directory, 0755, true);
        }

        // Copy file to storage
        File::copy($sourceFile, storage_path('app/public/' . $filePath));

        // Determine orientation for images
        $orientation = 'Portrait';
        if (in_array($mediaType, ['Photo', 'Illustration'])) {
            if (File::exists($sourceFile)) {
                list($width, $height) = getimagesize($sourceFile);
                $orientation = $width > $height ? 'Landscape' : 'Portrait';
            }
        }

        // Set thumbnail path based on media type
        $thumbnailPath = null;
        if ($mediaType == 'Photo' || $mediaType == 'Illustration') {
            // For images, create a thumbnail in the user's thumbnail directory
            $thumbnailFilename = pathinfo($uniqueFilename, PATHINFO_FILENAME) . '.jpg';
            $thumbnailPath = StorageHelper::getUserThumbnailPath($authorId, $thumbnailFilename);

            // Copy the image as thumbnail
            File::copy(storage_path('app/public/' . $filePath), storage_path('app/public/' . $thumbnailPath));

        } elseif ($mediaType == 'Video') {
            // Generate thumbnail for video using FFmpeg
            $thumbnailFilename = pathinfo($uniqueFilename, PATHINFO_FILENAME) . '.jpg';
            $thumbnailPath = StorageHelper::getUserThumbnailPath($authorId, $thumbnailFilename);

            try {
                // Use FFMpeg to extract a frame from the video
                \FFMpeg::fromDisk('public')
                    ->open($filePath)
                    ->getFrameFromSeconds(2)
                    ->export()
                    ->toDisk('public')
                    ->save($thumbnailPath);

                $this->command->info("Generated video thumbnail: $thumbnailPath");
            } catch (\Exception $e) {
                $this->command->error("Failed to generate video thumbnail: " . $e->getMessage());
                // Fallback to default thumbnail
                $thumbnailPath = 'thumbnail/thumbnail_video.png';
            }
        } elseif ($mediaType == 'Audio') {
            $thumbnailPath = 'thumbnail/thumbnail_audio.svg';
        } elseif ($mediaType == 'Document') {
            $extension = strtolower(pathinfo($filename, PATHINFO_EXTENSION));
            if ($extension == 'pdf') {
                $thumbnailPath = 'thumbnail/thumbnail_pdf.png';
            } elseif (in_array($extension, ['xls', 'xlsx', 'xlsm'])) {
                $thumbnailPath = 'thumbnail/thumbnail_excel.png';
            } else {
                $thumbnailPath = 'thumbnail/thumbnail_doc.png';
            }
        }

        // Create content
        $contentId = Uuid::uuid4();

        // Use provided description or generate a default one
        $contentDescription = $description ?? "This is a sample $mediaType content with $status status.";

        // Generate a random creator name
        $creatorNames = [
            'John Smith', 'Maria Garcia', 'Ahmed Khan', 'Li Wei', 'Sophia Johnson',
            'Carlos Rodriguez', 'Aisha Patel', 'Hiroshi Tanaka', 'Emma Wilson', 'Jamal Brown'
        ];
        $creatorName = $creatorNames[array_rand($creatorNames)];

        // Prepare content data
        $contentData = [
            'id' => $contentId,
            'author_id' => $authorId,
            'category_id' => $categoryId,
            'category_2_id' => $category2Id,
            'description' => $contentDescription,
            'filename' => $uniqueFilename,
            'original_filename' => $filename,
            'file_path' => $filePath,
            'thumbnail_path' => $thumbnailPath,
            'media_type' => $mediaType,
            'orientation' => $orientation,
            'creator_name' => $creatorName,
            'date_taken' => Carbon::now()->subDays(rand(1, 365)), // More variety in dates
            'status' => $status,
            'upload_date' => Carbon::now()->subDays($daysAgo),
            'downloads_count' => rand(0, 500), // More variety in download counts
            // Initialize new fields
            'is_archived' => false,
            'archived_at' => null,
            'archived_reason' => null,
            'is_hidden' => false,
            'hidden_at' => null,
            'hidden_reason' => null,
        ];

        // Set status-specific timestamps for archiving functionality
        if ($status === 'Published' && $wasApproved) {
            // This is approved content that was published
            $contentData['approved_at'] = Carbon::now()->subDays($daysAgo);

            // For testing: Hide approved content older than 14 days (only in Submit/Review pages)
            if ($daysAgo > 14) {
                $contentData['is_hidden'] = true;
                $contentData['hidden_at'] = Carbon::now()->subDays($daysAgo - 14);
                $contentData['hidden_reason'] = 'Auto-hidden after 14 days of approval (seeder test data)';
            }
        } elseif ($status === 'Rejected') {
            $contentData['rejected_at'] = Carbon::now()->subDays($daysAgo);

            // For testing: Archive rejected content older than 14 days
            if ($daysAgo > 14) {
                $contentData['is_archived'] = true;
                $contentData['archived_at'] = Carbon::now()->subDays($daysAgo - 14);
                $contentData['archived_reason'] = 'Auto-archived after 14 days of rejection (seeder test data)';
            }
        }

        $content = Content::create($contentData);

        // Attach random keywords (3-8 for more variety)
        $keywordCount = rand(3, 8);
        $selectedKeywords = $keywords->random($keywordCount);

        foreach ($selectedKeywords as $keyword) {
            ContentKeyword::create([
                'content_id' => $contentId,
                'keyword_id' => $keyword->id
            ]);
        }

        // Add review if status is Rejected or Published (and was approved)
        if ($status === 'Rejected' || ($status === 'Published' && $wasApproved)) {
            $reviewStatus = ($status == 'Rejected') ? 'Refused' : 'Approved';

            // Create more detailed comments based on status
            $comments = "";
            if ($status == 'Rejected') {
                $rejectionReasons = [
                    "This content does not meet our quality standards.",
                    "The content contains inappropriate material.",
                    "The file format is not supported or is corrupted.",
                    "The content lacks necessary information or context.",
                    "The content violates our terms of service."
                ];
                $comments = $rejectionReasons[array_rand($rejectionReasons)];
            } else { // Published (was approved)
                $approvalComments = [
                    "This content meets all our quality standards and has been approved for publication.",
                    "Excellent submission! Approved and published.",
                    "Content reviewed and approved for the library.",
                    "Quality content that adds value to our collection. Approved.",
                    "Content has been reviewed and meets all requirements. Published."
                ];
                $comments = $approvalComments[array_rand($approvalComments)];
            }

            ContentReview::create([
                'id' => Uuid::uuid4(),
                'content_id' => $contentId,
                'reviewer_id' => $reviewerId,
                'status' => $reviewStatus,
                'comments' => $comments,
                'review_date' => Carbon::now()->subDays($daysAgo),
            ]);
        }

        $this->command->info("Created $mediaType content with $status status: $uniqueFilename");
    }

    /**
     * Get MIME type folder from media type
     */
    private function getMimeTypeFromMediaType($mediaType)
    {
        switch ($mediaType) {
            case 'Photo':
            case 'Illustration':
                return 'image';
            case 'Video':
                return 'video';
            case 'Audio':
                return 'audio';
            case 'Document':
                return 'application';
            default:
                return 'application';
        }
    }

    /**
     * Create sample keywords
     */
    private function createKeywords()
    {
        $keywordNames = [
            // Education
            'education', 'learning', 'school', 'university', 'knowledge', 'teaching', 'academic', 'study', 'classroom', 'curriculum',

            // Science & Technology
            'science', 'technology', 'innovation', 'research', 'development', 'engineering', 'data', 'artificial intelligence', 'robotics', 'biotechnology',

            // Arts & Culture
            'art', 'culture', 'history', 'heritage', 'tradition', 'painting', 'sculpture', 'literature', 'theater', 'museum',

            // Nature & Environment
            'nature', 'environment', 'sustainability', 'conservation', 'ecology', 'wildlife', 'ocean', 'forest', 'climate', 'biodiversity',

            // Health & Wellness
            'health', 'wellness', 'fitness', 'nutrition', 'medicine', 'mental health', 'yoga', 'meditation', 'healthcare', 'exercise',

            // Business & Economy
            'business', 'economy', 'finance', 'entrepreneurship', 'management', 'marketing', 'investment', 'startup', 'leadership', 'strategy',

            // Social & Community
            'social', 'community', 'society', 'people', 'humanity', 'diversity', 'inclusion', 'equality', 'collaboration', 'teamwork',

            // Digital & Online
            'digital', 'online', 'internet', 'virtual', 'cyber', 'web', 'mobile', 'app', 'social media', 'e-commerce',

            // Creative & Visual
            'creative', 'design', 'illustration', 'photography', 'visual', 'graphic design', 'animation', 'typography', 'branding', 'color',

            // Audio & Music
            'audio', 'music', 'sound', 'podcast', 'recording', 'voice', 'instrumental', 'acoustic', 'production', 'composition',

            // Travel & Places
            'travel', 'destination', 'adventure', 'tourism', 'landscape', 'city', 'architecture', 'landmark', 'exploration', 'journey',

            // Food & Cuisine
            'food', 'cuisine', 'cooking', 'recipe', 'gastronomy', 'culinary', 'restaurant', 'ingredient', 'beverage', 'dining'
        ];

        $keywords = collect();

        foreach ($keywordNames as $name) {
            $keyword = Keyword::firstOrCreate(['name' => $name]);
            $keywords->push($keyword);
        }

        return $keywords;
    }
}
