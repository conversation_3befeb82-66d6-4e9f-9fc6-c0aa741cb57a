<?php

namespace App\Services;

use App\Models\User;
use App\Repositories\Contracts\ContentRepositoryInterface;
use App\Repositories\Contracts\LogRepositoryInterface;
use App\Repositories\Contracts\NotificationRepositoryInterface;
use App\Repositories\Contracts\ReviewRepositoryInterface;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ReviewService
{
  protected $contentRepository, $reviewRepository, $logRepository, $notificationRepository;

  public function __construct(
    ContentRepositoryInterface $contentRepository,
    ReviewRepositoryInterface $reviewRepository,
    LogRepositoryInterface $logRepository,
    NotificationRepositoryInterface $notificationRepository,
    )
  {
    $this->contentRepository = $contentRepository;
    $this->reviewRepository = $reviewRepository;
    $this->logRepository = $logRepository;
    $this->notificationRepository = $notificationRepository;
  }

  public function findOne(string $id)
  {
    return $this->contentRepository->findOne($id);
  }

  public function getContent($request)
  {
    try {
      if($request['status'] == "Reviewed") $request['status'] = ['Rejected', 'Approved'];
      // Special case for Approved tab - show both Approved and Published content
      if($request['status'] == "Approved") $request['status'] = ['Approved', 'Published'];
      // if($request['media_type'] == "image") $request['media_type'] = ['Photo', 'Illustration'];
      switch ($request['media_type']) {
        case 'image':
          $request['media_type'] = ['Photo', 'Illustration'];
          break;
        case 'video':
          $request['media_type'] = ['Video'];
          break;
        case 'audio':
          $request['media_type'] = ['Audio'];
          break;
        case 'document':
          $request['media_type'] = ['Document'];
          break;
        case 'index':
          $request['media_type'] = ['Photo', 'Illustration', 'Video', 'Audio', 'Document'];
          break;
        default:
          break;
      }

      // Hide approved content older than 14 days in Review Content page (only for Approved tab)
      if (isset($request['status']) && in_array('Approved', (array)$request['status'])) {
        $request['hide_approved_content'] = true;
      }

      return $this->contentRepository->getContent($request);
    } catch (\Exception $e) {
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function getReviews($request)
  {
    try {
      $data = $this->reviewRepository->getReviews($request);
      foreach($data as $item){
        $item['review_date'] = Carbon::parse($item['review_date'])->timezone('Asia/Jakarta')->format('d-m-Y H:i');
      }
      return $data;
    } catch (\Exception $e) {
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function countContent()
  {
    $counts = $this->contentRepository->countContent();

    // Convert the Eloquent model to a plain array with numeric values
    $result = [];
    foreach ((array)$counts as $key => $value) {
      // Skip Eloquent internal properties
      if (in_array($key, ['incrementing', 'preventsLazyLoading', 'exists', 'wasRecentlyCreated', 'timestamps', 'usesUniqueIds'])) {
        continue;
      }
      $result[$key] = (int)($value ?? 0);
    }

    // Debug: Log the counts to see what's being returned
    Log::info('Content counts:', $result);

    return $result;
  }

  public function approval($request)
  {
    DB::beginTransaction();
    try {
      $status = $request['type'] == "approve" ? "Published" : "Rejected"; // Set directly to "Published" status
      $allApprovalSucceeded = true;

      foreach($request['id'] as $item){
        $data = [
          'id' => $item,
          'status' => $status,
          // Set the appropriate timestamp based on the action
          'approved_at' => $status === 'Published' ? now() : null,
          'rejected_at' => $status === 'Rejected' ? now() : null,
          'is_archived' => false // Ensure it's not archived when status changes
        ];

        $approval = $this->reviewRepository->approval($data);

        if($approval){
          $flag = $status == "Published" ? "Approved" : "Refused"; // Keep the review status as "Approved"

          $this->logRepository->log(
            auth()->user()->id,
            $item,
            'telah melakukan '.($status == "Published" ? "Approved dan Published" : "Rejected").' pada konten milik',
            'review',
            'book-open'
          );

          $review = [
            'content_id' => $item,
            'reviewer_id' => auth()->user()->id,
            'status' => $flag,
            'comments' => $request['comments'] ?? null,
            'review_date' => now(),
          ];

          $createReview = $this->reviewRepository->create($review);
            if (!$createReview) {
                $allApprovalSucceeded = false;
                break;
            }

          if($status == "Rejected"){
            $content = $this->contentRepository->findOne($item);
            $this->notificationRepository->create(
              $content->author_id,
              auth()->user()->id,
              "Reject",
              "telah melakukan reject konten"
            );
          }
        } else {
            $allApprovalSucceeded = false;
            break;
        }
      }

      if ($allApprovalSucceeded) {
        if($status == "Published"){
          // Notify department heads
          $dephead = User::whereHas('role', function($qry) {
            $qry->where('id', 2);
          })->pluck('id');

          foreach($dephead as $dep){
            $this->notificationRepository->create(
              $dep,
              auth()->user()->id,
              "Approve",
              "telah melakukan approve dan publish konten"
            );
          }

          // Notify content authors about approval and publication
          foreach($request['id'] as $item){
            $content = $this->contentRepository->findOne($item);
            $this->notificationRepository->create(
              $content->author_id,
              auth()->user()->id,
              "Approve",
              "telah melakukan approve dan publish konten"
            );
          }
        }

        DB::commit();
        return response()->json(['success' => true]);
      } else {
        DB::rollBack();
        return response()->json(['success' => false, 'message' => 'One or more approvals failed']);
      }
    } catch (\Exception $e) {
      DB::rollback();
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }

  public function publication($request)
  {
    DB::beginTransaction();
    try {
      $status = $request['type'] == "publish" ? "Published" : "Rejected";
      $allApprovalSucceeded = true;

      foreach($request['id'] as $item){
        $data = [
          'id' => $item,
          'status' => $status,
          // Set the appropriate timestamp based on the action
          'approved_at' => $status === 'Published' ? now() : null,
          'rejected_at' => $status === 'Rejected' ? now() : null,
          'is_archived' => false // Ensure it's not archived when status changes
        ];

        $publication = $this->reviewRepository->publication($data);

        if($publication){
          $reviewStatus = $status == "Published" ? "Approved" : "Refused";

          $this->logRepository->log(
            auth()->user()->id,
            $item,
            'telah melakukan '.$status.' pada konten milik',
            'review',
            'book-open'
          );

          $review = [
            'content_id' => $item,
            'reviewer_id' => auth()->user()->id,
            'status' => $reviewStatus,
            'comments' => $request['comments'] ?? null,
            'review_date' => now(),
          ];

          $createReview = $this->reviewRepository->create($review);
            if (!$createReview) {
                $allApprovalSucceeded = false;
                break;
            }

          $content = $this->contentRepository->findOne($item);
          $message = $request['type'] == "publish" ? "melakukan publikasi" : "menolak mempublikasikan";
          $this->notificationRepository->create(
            $content->author_id,
            auth()->user()->id,
            ucfirst($request['type']),
            "telah ".$message." konten"
          );
          
          // Update the content with the appropriate timestamp
          $updateData = [
            'status' => $status,
            'approved_at' => $status === 'Published' ? now() : $content->approved_at,
            'rejected_at' => $status === 'Rejected' ? now() : $content->rejected_at,
            'is_archived' => false // Ensure it's not archived when status changes
          ];
          $this->contentRepository->update($item, $updateData);
        } else {
            $allApprovalSucceeded = false;
            break;
        }
      }

      if ($allApprovalSucceeded) {
        DB::commit();
        return response()->json(['success' => true]);
      } else {
        DB::rollBack();
        return response()->json(['success' => false, 'message' => 'One or more publications failed']);
      }
    } catch (\Exception $e) {
      DB::rollback();
      error_log($e);
      Log::error($e);
      throw $e;
    }
  }
}