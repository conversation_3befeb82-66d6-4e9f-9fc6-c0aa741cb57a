<?php

namespace App\Http\Controllers;

use App\Models\Content;
use App\Services\ReviewService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;

class ReviewController extends Controller
{
    protected $page = "Review Content";

    protected $reviewService;

    public function __construct(ReviewService $reviewService)
    {
        $this->reviewService = $reviewService;
    }

    public function index()
    {
        try {
            $data = [
                'page' => $this->page,
            ];

            return view("pages.review-content.index", $data);
        } catch (\Exception $e) {
            abort(500, $e->getMessage());
        }
    }

    public function get_contents(Request $request)
    {
        try {
            // Get the content first
            $content = $this->reviewService->getContent($request->all());

            // Get counts directly from the database for all statuses
            $countArray = [];

            // Get count for Pending
            $countArray['pending'] = Content::where('status', 'Pending')->count();
            $countArray['pending_image'] = Content::where('status', 'Pending')
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['pending_video'] = Content::where('status', 'Pending')
                ->where('media_type', 'Video')->count();
            $countArray['pending_audio'] = Content::where('status', 'Pending')
                ->where('media_type', 'Audio')->count();
            $countArray['pending_document'] = Content::where('status', 'Pending')
                ->where('media_type', 'Document')->count();

            // Get count for Rejected (last 14 days, exclude archived)
            $fourteenDaysAgo = now()->subDays(14);
            $countArray['rejected'] = Content::where('status', 'Rejected')
                ->where('rejected_at', '>=', $fourteenDaysAgo)
                ->where('is_archived', false)->count();
            $countArray['rejected_image'] = Content::where('status', 'Rejected')
                ->where('rejected_at', '>=', $fourteenDaysAgo)
                ->where('is_archived', false)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['rejected_video'] = Content::where('status', 'Rejected')
                ->where('rejected_at', '>=', $fourteenDaysAgo)
                ->where('is_archived', false)
                ->where('media_type', 'Video')->count();
            $countArray['rejected_audio'] = Content::where('status', 'Rejected')
                ->where('rejected_at', '>=', $fourteenDaysAgo)
                ->where('is_archived', false)
                ->where('media_type', 'Audio')->count();
            $countArray['rejected_document'] = Content::where('status', 'Rejected')
                ->where('rejected_at', '>=', $fourteenDaysAgo)
                ->where('is_archived', false)
                ->where('media_type', 'Document')->count();

            // Get count for Approved (Published content that was approved, exclude hidden)
            $countArray['approved'] = Content::where('status', 'Published')
                ->whereNotNull('approved_at')
                ->where('is_hidden', false)->count();
            $countArray['approved_image'] = Content::where('status', 'Published')
                ->whereNotNull('approved_at')
                ->where('is_hidden', false)
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['approved_video'] = Content::where('status', 'Published')
                ->whereNotNull('approved_at')
                ->where('is_hidden', false)
                ->where('media_type', 'Video')->count();
            $countArray['approved_audio'] = Content::where('status', 'Published')
                ->whereNotNull('approved_at')
                ->where('is_hidden', false)
                ->where('media_type', 'Audio')->count();
            $countArray['approved_document'] = Content::where('status', 'Published')
                ->whereNotNull('approved_at')
                ->where('is_hidden', false)
                ->where('media_type', 'Document')->count();

            // Get count for Published
            $countArray['published'] = Content::where('status', 'Published')->count();
            $countArray['published_image'] = Content::where('status', 'Published')
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['published_video'] = Content::where('status', 'Published')
                ->where('media_type', 'Video')->count();
            $countArray['published_audio'] = Content::where('status', 'Published')
                ->where('media_type', 'Audio')->count();
            $countArray['published_document'] = Content::where('status', 'Published')
                ->where('media_type', 'Document')->count();

            // Calculate Reviewed (Approved + Rejected)
            $countArray['reviewed'] = $countArray['approved'] + $countArray['rejected'];
            $countArray['reviewed_image'] = $countArray['approved_image'] + $countArray['rejected_image'];
            $countArray['reviewed_video'] = $countArray['approved_video'] + $countArray['rejected_video'];
            $countArray['reviewed_audio'] = $countArray['approved_audio'] + $countArray['rejected_audio'];
            $countArray['reviewed_document'] = $countArray['approved_document'] + $countArray['rejected_document'];

            // Add Draft counts for consistency (even though they're not used in Review Content)
            $countArray['draft'] = Content::where('status', 'Draft')->count();
            $countArray['draft_image'] = Content::where('status', 'Draft')
                ->whereIn('media_type', ['Photo', 'Illustration'])->count();
            $countArray['draft_video'] = Content::where('status', 'Draft')
                ->where('media_type', 'Video')->count();
            $countArray['draft_audio'] = Content::where('status', 'Draft')
                ->where('media_type', 'Audio')->count();
            $countArray['draft_document'] = Content::where('status', 'Draft')
                ->where('media_type', 'Document')->count();

            $data = [
                'count' => $countArray,
                'content' => $content
            ];

            return response()->json([
                'success' => true,
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => config('messages.error.get'),
                'error' => $e->getMessage()
            ], 500);
        }
    }

    public function approval(Request $request)
    {
        try {
            $data = $this->reviewService->approval($request);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.'.$request['type']),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.'.$request['type'])
            ], 500);
        }
    }

    public function publication(Request $request)
    {
        try {
            $data = $this->reviewService->publication($request);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.'.$request['type']),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.publish')
            ], 500);
        }
    }

    public function get_reviews($id)
    {
        try {
            $data = $this->reviewService->getReviews($id);

            return response()->json([
                'status' => 'success',
                'message' => config('messages.success.get'),
                'data' => $data
            ], 200);
        } catch (\Exception $e) {
            return response()->json([
                'status' => 'error',
                'message' => config('messages.error.get')
            ], 500);
        }
    }

    /**
     * View a document directly in the browser
     *
     * @param int $id Content ID
     * @return \Illuminate\Http\Response
     */
    public function viewDocument($id)
    {
        try {
            // Get the content item
            $content = Content::findOrFail($id);

            // Check if it's a document
            if ($content->media_type !== 'Document') {
                return response('File is not a document', 400);
            }

            // Get file extension
            $extension = strtolower(pathinfo($content->file_path, PATHINFO_EXTENSION));

            // If it's a PDF, stream it directly
            if ($extension === 'pdf') {
                return response()->file(storage_path('app/public/' . $content->file_path));
            }

            // If it's a DOC/DOCX, try to convert it
            if (in_array($extension, ['doc', 'docx'])) {
                // Inject DocumentConversionService
                $documentConversionService = app(\App\Services\DocumentConversionService::class);

                // Check if PDF version already exists
                $pdfPath = $documentConversionService->getPdfVersion($content->file_path);

                // If not, convert it
                if (!$pdfPath) {
                    $pdfPath = $documentConversionService->convertToPdf($content->file_path);

                    if (!$pdfPath) {
                        return response('Failed to convert document', 500);
                    }
                }

                // Stream the PDF file
                return response()->file(storage_path('app/public/' . $pdfPath));
            }

            // For other file types, just download
            return response()->download(storage_path('app/public/' . $content->file_path));

        } catch (\Exception $e) {
            \Illuminate\Support\Facades\Log::error('Document view error: ' . $e->getMessage());
            return response('Error viewing document: ' . $e->getMessage(), 500);
        }
    }
}
